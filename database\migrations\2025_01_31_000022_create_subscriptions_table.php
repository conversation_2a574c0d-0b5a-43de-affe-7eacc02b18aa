<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();

            $table->foreignId('id_subs_type')->constrained('subs_types');
            $table->foreignId('id_client')->constrained('clients');
            $table->foreignId('id_payment_method')->nullable()->constrained('payment_methods');

            $table->string('ref')->unique();

            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();

            $table->foreignId('id_trip')->nullable()->constrained('trips');
            $table->boolean('is_reversed')->default(false);

            $table->foreignId('id_periodicity')->nullable()->constrained('periodicities');
            $table->string('photo')->nullable();
            $table->boolean('is_social_affair')->default(false);

            $table->boolean('hasVacances')->default(false);
            $table->string('rest_days')->nullable();

            $table->integer('subs_number')->nullable(); 

            $table->enum('status', ['PAYED', 'NOTPAYED', 'CANCELED', 'PENDING'])->default('NOTPAYED');
            $table->enum('special_client', ['CIVIL', 'SCOLAIRE', 'UNIVERSITAIRE'])->default(null)->nullable();
            $table->boolean('is_printed')->default(false); 

            $table->boolean('is_stagiaire')->default(false);
            $table->date('stage_date_start')->nullable();
            $table->date('stage_date_end')->nullable();

            $table->bigInteger('id_subscriber')->nullable()->constrained('subscribers');
            $table->foreignId('id_agent')->nullable()->constrained('admins');
            $table->foreignId('id_affectation')->nullable()->constrained('affectation_agents');
            $table->foreignId('id_sale_point')->nullable()->constrained('sale_points');

            $table->foreignId('id_parent')->nullable()->references('id')->on('subscriptions');
            $table->date('renewal_date')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};

