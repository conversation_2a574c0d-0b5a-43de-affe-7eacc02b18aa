# SRTGN Database Schema Documentation

This document provides a comprehensive overview of the SRTGN database schema, including all tables, their fields, and relationships.

## Table of Contents

- [User Management](#user-management)
- [Geographic Data](#geographic-data)
- [Transportation System](#transportation-system)
- [Subscription System](#subscription-system)
- [Payment System](#payment-system)
- [Vehicle and Location Management](#vehicle-and-location-management)
- [System Configuration](#system-configuration)
- [Authentication and Authorization](#authentication-and-authorization)

## User Management

### Admins

Administrative users of the system.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| lastname | varchar(255) | Last name |
| firstname | varchar(255) | First name |
| phone | varchar(255) | Phone number (unique) |
| cin | varchar(255) | National ID number (unique) |
| address | varchar(255) | Address |
| email | varchar(255) | Email address (unique) |
| password | varchar(255) | Encrypted password |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Clients

End users/customers of the system.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| lastname | varchar(255) | Last name |
| firstname | varchar(255) | First name |
| dob | date | Date of birth |
| phone | bigint(20) | Phone number (unique) |
| identity_number | varchar(255) | National ID number (unique) |
| address | varchar(255) | Address |
| email | varchar(255) | Email address (unique) |
| password | varchar(255) | Encrypted password |
| id_client_type | bigint(20) | Foreign key to type_clients |
| id_delegation | bigint(20) | Foreign key to delegations |
| id_governorate | bigint(20) | Foreign key to governorates |
| id_establishment | bigint(20) | Foreign key to establishments (nullable) |
| id_degree | bigint(20) | Foreign key to degrees (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Type_Clients

Types of clients in the system.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| color | varchar(255) | Color code |
| is_student | tinyint(1) | Whether this type is for students |
| hasCIN | tinyint(1) | Whether ID is required |
| is_impersonal | tinyint(1) | Whether the subscription is impersonal |
| is_conventional | tinyint(1) | Whether it's a conventional type |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Geographic Data

### Governorates

Top-level administrative divisions.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| code | int(11) | Governorate code (unique) |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Delegations

Administrative subdivisions of governorates.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| id_governorate | bigint(20) | Foreign key to governorates |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Agencies

Service agencies within geographic areas.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| code | varchar(255) | Agency code |
| contact | varchar(255) | Contact information |
| address | varchar(255) | Physical address |
| id_delegation | bigint(20) | Foreign key to delegations |
| id_governorate | bigint(20) | Foreign key to governorates |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Sale_Points

Points of sale for subscriptions.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| contact | varchar(255) | Contact information |
| address | varchar(255) | Physical address |
| status | tinyint(1) | Status (active/inactive) |
| id_delegation | bigint(20) | Foreign key to delegations |
| id_governorate | bigint(20) | Foreign key to governorates |
| id_agency | bigint(20) | Foreign key to agencies |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Transportation System

### Lines

Transportation lines/routes.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| CODE_LINE | varchar(255) | Line code |
| type_service | enum('normal','confort') | Service type |
| status | tinyint(1) | Status (active/inactive) |
| commercial_speed | int(11) | Commercial speed |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Stations

Transportation stations.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| longitude | varchar(255) | Longitude coordinate |
| latitude | varchar(255) | Latitude coordinate |
| id_delegation | bigint(20) | Foreign key to delegations (nullable) |
| id_governorate | bigint(20) | Foreign key to governorates (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Line_Stations

Junction table linking lines and stations.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_line | bigint(20) | Foreign key to lines |
| id_station | bigint(20) | Foreign key to stations |
| position | int(11) | Position of station in the line |
| type | enum('HIDDEN','TERMINUS','INTER') | Station type |
| direction | int(11) | Direction (nullable) |
| start_time | json | Start times (JSON format) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Trips

Defined trips between stations on a line.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| id_line | bigint(20) | Foreign key to lines |
| id_station_start | bigint(20) | Foreign key to stations (start) |
| id_station_end | bigint(20) | Foreign key to stations (end) |
| status | tinyint(1) | Status (active/inactive) |
| inter_station | tinyint(1) | Whether intermediate stations are included |
| number_of_km | int(11) | Distance in kilometers |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Website_Trips

Trips specifically for website display.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| code | varchar(255) | Trip code |
| id_line | bigint(20) | Foreign key to lines |
| id_station_start | bigint(20) | Foreign key to stations (start) |
| id_station_end | bigint(20) | Foreign key to stations (end) |
| number_of_km | int(11) | Distance in kilometers |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Subscription System

### Subs_Types

Types of subscriptions available.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| color | varchar(255) | Color code |
| is_student | tinyint(1) | Whether for students |
| hasCIN | tinyint(1) | Whether ID is required |
| is_impersonal | tinyint(1) | Whether impersonal |
| is_conventional | tinyint(1) | Whether conventional |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Subscriptions

User subscriptions.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_subs_type | bigint(20) | Foreign key to subs_types |
| id_client | bigint(20) | Foreign key to clients |
| id_payment_method | bigint(20) | Foreign key to payment_methods (nullable) |
| start_date | date | Start date (nullable) |
| end_date | date | End date (nullable) |
| id_trip | bigint(20) | Foreign key to trips (nullable) |
| is_reversed | tinyint(1) | Whether the trip direction is reversed |
| id_periodicity | bigint(20) | Foreign key to periodicities (nullable) |
| photo | varchar(255) | Photo path (nullable) |
| is_social_affair | tinyint(1) | Whether it's a social affair subscription |
| hasVacances | tinyint(1) | Whether it includes vacation periods |
| rest_days | varchar(255) | Rest days (nullable) |
| subs_number | int(11) | Subscription number (nullable) |
| status | enum('PAYED','NOTPAYED','CANCELED') | Payment status |
| id_parent | bigint(20) | Foreign key to parent subscription (nullable) |
| renewal_date | date | Renewal date (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Periodicities

Subscription periodicities.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| periodicity_code | varchar(255) | Periodicity code |
| max_days_per_week | int(11) | Maximum days per week |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Campaigns

Marketing campaigns.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Sale_Periods

Periods for subscription sales.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| date_start | date | Start date |
| date_end | date | End date |
| id_campaign | bigint(20) | Foreign key to campaigns |
| id_abn_type | bigint(20) | Foreign key to subs_types |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Seasons

Seasonal periods.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| start_date | date | Start date |
| end_date | date | End date |
| priority | int(11) | Priority level |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Discounts

Discount offerings.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| percentage | decimal(5,2) | Discount percentage |
| id_client_type | bigint(20) | Foreign key to type_clients |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Discount_Periodicity

Junction table linking discounts and periodicities.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| discount_id | bigint(20) | Foreign key to discounts |
| periodicity_id | bigint(20) | Foreign key to periodicities |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Card Management

### Card_Types

Types of cards issued.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Card_Fees

Fees associated with card types.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| amount | int(11) | Fee amount |
| id_subs_type | bigint(20) | Foreign key to subs_types |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Stock_Cards

Card stock inventory.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| name | varchar(255) | Stock name |
| id_card_type | bigint(20) | Foreign key to card_types |
| initial_quantity | int(11) | Initial quantity |
| status | int(11) | Status code |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Subs_Cards

Cards associated with subscriptions.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_card_type | bigint(20) | Foreign key to card_types |
| id_sale_point | bigint(20) | Foreign key to sale_points |
| id_subscription | bigint(20) | Foreign key to subscriptions |
| id_motif_duplicate | bigint(20) | Foreign key to motif_duplicates |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Motif_Duplicates

Reasons for card duplication.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Affectation_Card_Types

Card type assignments.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| start_serial_number | int(11) | Start serial number |
| end_serial_number | int(11) | End serial number |
| id_card_type | bigint(20) | Foreign key to card_types (nullable) |
| id_affectation_agent | bigint(20) | Foreign key to affectation_agents (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Payment System

### Payment_Methods

Methods of payment.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Transactions

Payment transactions.

| Field | Type | Description |
|-------|------|-------------|
| payment_id | bigint(20) | Primary key |
| subscription_id | bigint(20) | Foreign key to subscriptions |
| client_id | bigint(20) | Foreign key to clients |
| amount | decimal(10,2) | Payment amount |
| payment_date | timestamp | Payment date |
| payment_mode | enum('en_ligne','guichet') | Payment mode |
| payment_method_id | bigint(20) | Foreign key to payment_methods |
| status | enum('completed','pending','failed','refunded') | Payment status |
| transaction_reference | varchar(255) | Reference number (nullable) |
| online_gateway | varchar(50) | Online payment gateway (nullable) |
| sale_point_id | bigint(20) | Foreign key to sale_points (nullable) |
| employee_id | bigint(20) | Foreign key to admins (nullable) |
| online_user_agent | text | User agent for online payments (nullable) |
| notes | text | Additional notes (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Tariff_Bases

Base tariffs.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| for_website | tinyint(1) | Whether displayed on website |
| tariffPerKM | decimal(10,2) | Tariff per kilometer |
| date | date | Effective date |
| id_subs_type | bigint(20) | Foreign key to subs_types (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Tariff_Options

Tariff options for trips.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_trip | bigint(20) | Foreign key to trips |
| id_subs_type | bigint(20) | Foreign key to subs_types |
| is_regular | tinyint(1) | Whether it's a regular tariff |
| id_tariff_base | bigint(20) | Foreign key to tariff_bases (nullable) |
| manual_tariff | int(11) | Manual tariff amount (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Governorate_Purchase_Orders

Purchase orders for governorates.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| ref | varchar(255) | Reference number |
| initial_amount | decimal(10,2) | Initial order amount |
| current_amount | decimal(10,2) | Current remaining amount |
| status | tinyint(1) | Status |
| date | date | Order date |
| id_governorate | bigint(20) | Foreign key to governorates |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Educational System

### Type_Establishments

Types of educational establishments.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Establishments

Educational establishments.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| abbreviation | varchar(255) | Abbreviation |
| id_delegation | bigint(20) | Foreign key to delegations |
| id_type_establishment | bigint(20) | Foreign key to type_establishments |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Degrees

Educational degrees.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| age_max | int(11) | Maximum age |
| id_type_establishment | bigint(20) | Foreign key to type_establishments |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Vehicle and Location Management

### Type_Vehicules

Types of vehicles.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| code | varchar(255) | Vehicle type code (unique) |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| nbre_max_place | int(11) | Maximum number of seats (nullable) |
| swf | varchar(255) | SWF file path (nullable) |
| photo | varchar(255) | Photo path (nullable) |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Location_Types

Types of vehicle locations/rentals.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| code | varchar(255) | Location type code (unique) |
| documents | varchar(255) | Required documents (nullable) |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Location_Seasons

Seasonal periods for vehicle locations.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| nom_fr | varchar(255) | Name in French |
| nom_en | varchar(255) | Name in English |
| nom_ar | varchar(255) | Name in Arabic |
| start_date | date | Start date |
| end_date | date | End date |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Type_Vehicle_Type_Locations

Junction table linking vehicle types and location types.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_type_vehicule | bigint(20) | Foreign key to type_vehicules |
| id_type_location | bigint(20) | Foreign key to location_types |
| km_min | int(11) | Minimum kilometers (nullable) |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Type_Vehicule_Saison_Locations

Junction table linking vehicle types and location seasons.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_type_vehicule | bigint(20) | Foreign key to type_vehicules |
| id_saison_location | bigint(20) | Foreign key to location_seasons |
| prix_km | decimal(10,2) | Price per kilometer (nullable) |
| status | tinyint(1) | Status (active/inactive) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Agent Management

### Affectation_Agents

Agent assignments to sale points.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| id_agent | bigint(20) | Foreign key to admins |
| id_sale_point | bigint(20) | Foreign key to sale_points |
| id_sale_period | bigint(20) | Foreign key to sale_periods |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## System Configuration

### Configs

System configuration settings.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| key | varchar(255) | Configuration key (unique) |
| value | text | Configuration value (nullable) |
| type | varchar(255) | Value type |
| group | varchar(255) | Configuration group (nullable) |
| label_fr | varchar(255) | Label in French (nullable) |
| label_en | varchar(255) | Label in English (nullable) |
| label_ar | varchar(255) | Label in Arabic (nullable) |
| description_fr | text | Description in French (nullable) |
| description_en | text | Description in English (nullable) |
| description_ar | text | Description in Arabic (nullable) |
| is_public | tinyint(1) | Whether publicly accessible |
| is_system | tinyint(1) | Whether it's a system setting |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

## Authentication and Authorization

### Roles

User roles.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| name | varchar(255) | Role name |
| guard_name | varchar(255) | Guard name |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Permissions

User permissions.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| name | varchar(255) | Permission name |
| guard_name | varchar(255) | Guard name |
| category_id | bigint(20) | Foreign key to categories (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Categories

Permission categories.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| name | varchar(255) | Category name |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Role_Has_Permissions

Junction table linking roles and permissions.

| Field | Type | Description |
|-------|------|-------------|
| permission_id | bigint(20) | Foreign key to permissions |
| role_id | bigint(20) | Foreign key to roles |

### Model_Has_Permissions

Junction table linking models and permissions.

| Field | Type | Description |
|-------|------|-------------|
| permission_id | bigint(20) | Foreign key to permissions |
| model_type | varchar(255) | Model type |
| model_id | bigint(20) | Model ID |

### Model_Has_Roles

Junction table linking models and roles.

| Field | Type | Description |
|-------|------|-------------|
| role_id | bigint(20) | Foreign key to roles |
| model_type | varchar(255) | Model type |
| model_id | bigint(20) | Model ID |

## Laravel System Tables

### Migrations

Laravel migrations.

| Field | Type | Description |
|-------|------|-------------|
| id | int(10) | Primary key |
| migration | varchar(255) | Migration name |
| batch | int(11) | Migration batch |

### Failed_Jobs

Failed queue jobs.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| uuid | varchar(255) | Job UUID (unique) |
| connection | text | Queue connection |
| queue | text | Queue name |
| payload | longtext | Job payload |
| exception | longtext | Exception details |
| failed_at | timestamp | Failure timestamp |

### Personal_Access_Tokens

Personal access tokens for API authentication.

| Field | Type | Description |
|-------|------|-------------|
| id | bigint(20) | Primary key |
| tokenable_type | varchar(255) | Tokenable model type |
| tokenable_id | bigint(20) | Tokenable model ID |
| name | varchar(255) | Token name |
| token | varchar(64) | Hashed token (unique) |
| abilities | text | Token abilities (nullable) |
| last_used_at | timestamp | Last usage timestamp (nullable) |
| expires_at | timestamp | Expiration timestamp (nullable) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |

### Password_Reset_Tokens

Password reset tokens.

| Field | Type | Description |
|-------|------|-------------|
| email | varchar(255) | User email (primary key) |
| token | varchar(255) | Reset token |
| created_at | timestamp | Creation timestamp |
