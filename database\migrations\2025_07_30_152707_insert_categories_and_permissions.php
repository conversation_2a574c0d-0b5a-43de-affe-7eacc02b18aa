<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

class InsertCategoriesAndPermissions extends Migration
{
    public function up()
    {

        // Insertion des permissions dans la table permissions
        $categories = [
            'dashboard',
            'roles_permissions',
            'admins',
            'governorates',
            'delegations',
            'establishment_types',
            'school_degrees',
            'establishments',
            'seasons',
            'stations',
            'routes',
            'lines',
            'type_vehicules',
            'location_types',
            'location_seasons',
            'vehicle_season_pricing',
            'type_vehicle_type_locations',
            'campaigns',
            'sales_periods',
            'periodicities',
            'agencies',
            'sales_points',
            'assign_agents',
            'stock_cards',
            'card_types',
            'abn_types',
            'payment_methods',
            'duplicate_motifs',
            'tariff_bases',
            'cards_fees',
            'discounts',
            'client_types',
            'clients',
            'newSubs',
            'duplicata',
            'remise_except',
            'carte_impersonnels',
            'abn_impersonnels',
            'abn_convention_impersonnels',   
            'abn_trajet',
            'abn_establishments',
            'abn_photos_firstname_lastname',
            'reimpression',
            'editing_payed_subscriptions'
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => 'manage_' . $category,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        foreach ($categories as $permission) {
            $category = DB::table('categories')->where('name', 'manage_' . $permission)->first();
            if ($category) {
                if(
                    $category->name == "manage_abn_trajet" 
                    || $category->name == "manage_abn_establishments" 
                    || $category->name == "manage_abn_photos_firstname_lastname" 
                    || $category->name == "manage_reimpression" 
                    || $category->name == "manage_editing_payed_subscriptions"
                    ){
                    DB::table('permissions')->insert([
                        'name' => 'manage_' . $permission,
                        'category_id' => $category->id,
                        'guard_name' => 'api',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    continue;
                }

                DB::table('permissions')->insert([
                    'name' => 'manage_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'view_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'create_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'edit_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'delete_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    public function down()
    {

        DB::table('categories')->delete();

        DB::table('permissions')->delete();
    }


}
