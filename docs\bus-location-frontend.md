# Bus Location Frontend Implementation Guide

## API Endpoints

### Bus Location Calculation
- **Endpoint**: `POST /api/website/bus-locations`
- **Description**: Calculate bus location amount based on parameters
- **Request Body**:
  ```json
  {
    "id_location_type": number,    // Required, must exist in location_types table
    "bus_type": "NORMAL" | "CONFORT", // Required
    "start_date": "YYYY-MM-DD",    // Required
    "end_date": "YYYY-MM-DD",      // Required, must be >= start_date
    "number_of_km": number         // Required, minimum 0
  }
  ```

### Supporting Endpoints
- **GET /api/website/location-types**: Get all location types
- **GET /api/website/type-vehicules**: Get all vehicle types

## Required Forms/Components

### Location Calculator Form
- Location type selector (dropdown)
- Bus type selector (radio/dropdown)
- Date range picker
  - Start date
  - End date
- Kilometer input (number)
- Calculate button

### Location Type Display
- Name (fr/en/ar)
- Code
- Required documents list
- Status indicator

### Vehicle Type Display
- Name (fr/en/ar)
- Status indicator
- Associated location types
- Associated seasons

## Data Models Structure

### Location Type
```typescript
interface LocationType {
  id: number;
  nom_fr: string;
  nom_en: string;
  nom_ar: string;
  code: string;
  documents: string;
  status: boolean;
}
```

### Location Season
```typescript
interface LocationSeason {
  id: number;
  nom_fr: string;
  nom_en: string;
  nom_ar: string;
  start_date: Date;
  end_date: Date;
  status: boolean;
}
```

### Vehicle Type
```typescript
interface TypeVehicule {
  id: number;
  nom_fr: string;
  nom_en: string;
  nom_ar: string;
  status: boolean;
}
```

## Suggested Features

1. Dynamic pricing calculator
2. Location availability checker
3. Document requirements checklist
4. Seasonal pricing display
5. Multi-language support (FR/EN/AR)
6. Interactive calendar for date selection
7. Kilometer calculator with map integration
8. Price comparison between bus types
9. Location history viewer
10. Booking request form

## UI/UX Considerations

1. RTL support for Arabic
2. Clear pricing display
3. Responsive design for mobile users
4. Loading states for calculations
5. Error handling and validation messages
6. Accessible form controls
7. Clear document requirements
8. Interactive date selection
9. Dynamic price updates
10. Mobile-friendly inputs

## State Management Requirements

1. Location types cache
2. Vehicle types cache
3. Current calculation parameters
4. Loading states
5. Error states
6. Form validation state
7. Selected options
8. Calculation results
9. User preferences
10. Language selection

## Validation Rules

1. Valid location type selection
2. Valid bus type selection
3. Date range validation
  - End date after start date
  - Dates within allowed range
4. Minimum kilometer value
5. Maximum kilometer value
6. Required fields validation
7. Document requirements check
8. Season availability check
9. Location type availability
10. Vehicle type availability