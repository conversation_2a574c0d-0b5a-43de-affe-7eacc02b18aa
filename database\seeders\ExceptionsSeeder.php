<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;

use DB;
class ExceptionsSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     * @throws \Exception
     */
    public function run()
    {
        
        $categories = [
            'exceptions',
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => 'manage_' . $category,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }



        foreach ($categories as $permission) {
            $category = DB::table('categories')->where('name', 'manage_' . $permission)->first();
            if ($category) {
                DB::table('permissions')->insert([
                    'name' => 'manage_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'view_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'create_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'edit_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('permissions')->insert([
                    'name' => 'delete_' . $permission,
                    'category_id' => $category->id,
                    'guard_name' => 'api',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }  

    }
}
