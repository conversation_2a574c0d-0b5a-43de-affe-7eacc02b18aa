# API Externe - Transactions du Jour Précédent

## Vue d'ensemble

Cette API permet aux applications externes de récupérer les transactions effectuées le jour précédent. Elle est conçue pour les traitements par lots nocturnes et les intégrations système.

## Sécurité

L'API est protégée par une clé API qui doit être fournie dans chaque requête.

### Configuration de la clé API

1. Générer une nouvelle clé API :
```bash
php artisan api:generate-key
```

2. Ou afficher une clé sans modifier les fichiers :
```bash
php artisan api:generate-key --show
```

3. La clé sera automatiquement ajoutée à votre fichier `.env` :
```env
EXTERNAL_API_KEY=srtgn_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## Endpoint

### GET /api/external/transactions/previous-day

Récupère toutes les transactions effectuées le jour précédent.

#### Headers requis
```
X-API-Key: votre-cle-api-ici
Content-Type: application/json
```

#### Paramètres
Aucun paramètre requis.

#### Réponse

**Succès (200)**
```json
{
    "success": true,
    "date": "2024-01-15",
    "total_transactions": 25,
    "total_amount": "1250.50",
    "data": [
        {
            "payment_id": 1,
            "subscription_id": 123,
            "client_id": 456,
            "amount": "50.00",
            "payment_date": "2024-01-15T14:30:00.000000Z",
            "payment_mode": "guichet",
            "payment_method_id": 1,
            "status": "completed",
            "transaction_reference": "TXN123456",
            "sale_point_id": 5,
            "employee_id": 10,
            "notes": null,
            "created_at": "2024-01-15T14:30:00.000000Z",
            "updated_at": "2024-01-15T14:30:00.000000Z",
            "payment_details": null,
            "subscription": { ... },
            "client": { ... },
            "payment_method": { ... },
            "sale_point": { ... },
            "employee": { ... }
        }
    ]
}
```

**Erreur d'authentification (401)**
```json
{
    "success": false,
    "message": "API Key is required",
    "error": "Missing X-API-Key header or api_key parameter"
}
```

**Erreur serveur (500)**
```json
{
    "success": false,
    "message": "Error retrieving previous day transactions",
    "error": "Description de l'erreur"
}
```

## Exemples d'utilisation

### cURL
```bash
curl -H "X-API-Key: votre-cle-api" \
     -H "Content-Type: application/json" \
     https://votre-domaine.com/api/external/transactions/previous-day
```

### PHP
```php
$apiKey = 'votre-cle-api';
$url = 'https://votre-domaine.com/api/external/transactions/previous-day';

$headers = [
    'X-API-Key: ' . $apiKey,
    'Content-Type: application/json'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$data = json_decode($response, true);

curl_close($ch);
```

### Python
```python
import requests

api_key = 'votre-cle-api'
url = 'https://votre-domaine.com/api/external/transactions/previous-day'

headers = {
    'X-API-Key': api_key,
    'Content-Type': 'application/json'
}

response = requests.get(url, headers=headers)
data = response.json()
```

## Limitations

- **Rate Limiting**: 60 requêtes par minute
- **Données**: Seules les transactions du jour précédent sont retournées
- **Authentification**: Clé API obligatoire
- **Format**: JSON uniquement

## Sécurité et bonnes pratiques

1. **Stockage sécurisé**: Stockez la clé API de manière sécurisée
2. **HTTPS**: Utilisez toujours HTTPS en production
3. **Rotation**: Changez régulièrement la clé API
4. **Monitoring**: Surveillez les logs d'accès
5. **Restriction IP**: Considérez l'ajout d'une restriction par IP si nécessaire

## Support

Pour toute question ou problème, contactez l'équipe de développement.
