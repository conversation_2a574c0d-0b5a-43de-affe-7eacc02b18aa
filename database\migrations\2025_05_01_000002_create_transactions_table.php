<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id('payment_id');
            $table->foreignId('subscription_id')->constrained('subscriptions');
            $table->foreignId('client_id')->constrained('clients');
            $table->decimal('amount', 10, 2);
            $table->timestamp('payment_date')->useCurrent();
            $table->enum('payment_mode', ['en_ligne', 'guichet', 'social_affair']);
            $table->foreignId('payment_method_id')->nullable()->constrained('payment_methods');
            $table->enum('status', ['completed', 'pending', 'failed', 'refunded', 'canceled']);
            $table->string('transaction_reference', 255)->nullable();
            $table->string('online_gateway', 50)->nullable();
            $table->foreignId('sale_point_id')->nullable()->constrained('sale_points');
            $table->foreignId('employee_id')->nullable()->constrained('admins');
            $table->text('online_user_agent')->nullable();
            $table->text('notes')->nullable();
            $table->json('payment_details')->nullable()->comment('Stores detailed payment breakdown including tariff base, distances, fees, and discounts');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};

