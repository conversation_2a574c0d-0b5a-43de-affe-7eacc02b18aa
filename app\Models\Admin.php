<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class Admin extends Authenticatable implements JWTSubject
{
    use HasFactory, HasRoles;

    protected $table = 'admins';

    protected $fillable = [
        'lastname',
        'firstname',
        'phone',
        'cin',
        'address',
        'email',
        'password'
    ];

    protected $hidden = [
        'password',
    ];


    /**
     * Get all of the subs_duplications for the Admin
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subs_duplications(): HasMany
    {
        return $this->hasMany(SubsDuplication::class, 'admin_id', 'id');
    }

    public function affectationAgents(): HasMany
    {
        return $this->hasMany(AffectationAgent::class, 'id_agent');
    }
    public function stockCards(): Has<PERSON><PERSON>
    {
        return $this->hasMany(StockCard::class, 'id_agent');
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }
}

