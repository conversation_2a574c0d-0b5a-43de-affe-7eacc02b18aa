<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\CardType;
use App\Models\Client;
use App\Models\Discount;
use App\Models\Governorate;
use App\Models\Line;
use App\Models\MotifDuplicate;
use App\Models\PaymentMethod;
use App\Models\Periodicity;
use App\Models\SalePoint;
use App\Models\Station;
use App\Models\Subscription;
use App\Models\SubsType;
use App\Models\Transaction;
use App\Models\Trip;
use App\Models\TypeClient;
use App\Repositories\StatisticsRepository;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StatisticsController extends Controller
{
    private StatisticsRepository $repository;

    public function __construct(StatisticsRepository $repository)
    {
        $this->repository = $repository;
        $this->middleware('jwt.auth');
    }

    /**
     * Get dashboard summary statistics
     */
    public function dashboard(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subYear();
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();

        $totalSubscriptions = $request->has('start_date') || $request->has('end_date')
            ? Subscription::whereBetween('created_at', [$startDate, $endDate])->count()
            : Subscription::count();

        $totalRevenue = $request->has('start_date') || $request->has('end_date')
            ? Transaction::where('status', 'completed')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->sum('amount')
            : Transaction::where('status', 'completed')->sum('amount');


        $statusCounts = $request->has('start_date') || $request->has('end_date')
            ? Subscription::whereBetween('created_at', [$startDate, $endDate])
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray()
            : Subscription::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray();

        $allSubsTypes = SubsType::select('id', 'nom_fr', 'nom_en', 'nom_ar')->get()->toArray();

        $subscriptionsByTypeQuery = $request->has('start_date') || $request->has('end_date')
            ? Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
                ->whereBetween('subscriptions.created_at', [$startDate, $endDate])
                ->select('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar', DB::raw('count(*) as count'))
                ->groupBy('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar')
                ->get()
                ->keyBy('id')
            : Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
                ->select('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar', DB::raw('count(*) as count'))
                ->groupBy('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar')
                ->get()
                ->keyBy('id');

        $subscriptionsByType = [];
        foreach ($allSubsTypes as $subsType) {
            $id = $subsType['id'];
            $subscriptionsByType[] = [
                'id' => $id,
                'nom_fr' => $subsType['nom_fr'],
                'nom_en' => $subsType['nom_en'],
                'nom_ar' => $subsType['nom_ar'],
                'count' => $subscriptionsByTypeQuery->has($id) ? $subscriptionsByTypeQuery[$id]->count : 0
            ];
        }

        return response()->json([
            'total_subscriptions' => $totalSubscriptions,
            'total_revenue' => $totalRevenue,
            'subscription_status' => $statusCounts,
            'subscriptions_by_type' => $subscriptionsByType,
        ]);
    }

    /**
     * Get subscribers statistics by trip
     */
    public function subscribersByTrip(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'journey' => 'nullable|exists:trips,id',
        ]);

        $query = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('stations as start_station', 'trips.id_station_start', '=', 'start_station.id')
            ->join('stations as end_station', 'trips.id_station_end', '=', 'end_station.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        // Apply filters
        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('start_date')) {
            $query->where('subscriptions.created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('subscriptions.created_at', '<=', $request->input('end_date'));
        }

        if ($request->filled('journey') && $request->input('journey') !== 'all') {
            $query->where('subscriptions.id_trip', $request->input('journey'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('subscriptions.sale_period_id', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'trips.id as trip_id',
                'trips.nom_fr as trip_name_fr',
                'trips.nom_en as trip_name_en',
                'trips.nom_ar as trip_name_ar',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('trips.id', 'trips.nom_fr', 'trips.nom_en', 'trips.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');

        return response()->json([
            'data' => $results->map(function($item) {
                return [
                    'trip_id' => $item->trip_id,
                    'trip_name' => $item->start_station_fr . ' - ' . $item->end_station_fr,
                    'nom_fr' => $item->trip_name_fr,
                    'nom_en' => $item->trip_name_en,
                    'nom_ar' => $item->trip_name_ar,
                    'subscriber_count' => (int)$item->subscriber_count,
                    'total_amount' => (float)$item->total_amount
                ];
            }),
            'total_amount' => (float)$totalAmount
        ]);
    }

    /**
     * Get subscribers statistics by establishment
     */
    public function subscribersByEstablishment(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'establishment' => 'nullable|exists:establishments,id',
        ]);

        $query = Subscription::join('clients', 'subscriptions.id_client', '=', 'clients.id')
            ->leftJoin('establishments', 'clients.id_establishment', '=', 'establishments.id')
            ->join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            })
            ->where('subs_types.is_student', true);

        // Apply filters
        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('start_date')) {
            $query->where('subscriptions.created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('subscriptions.created_at', '<=', $request->input('end_date'));
        }

        if ($request->filled('establishment') && $request->input('establishment') !== 'all') {
            $query->where('clients.id_establishment', $request->input('establishment'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('subscriptions.sale_period_id', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'establishments.id as establishment_id',
                'establishments.nom_fr as establishment_name_fr',
                'establishments.nom_en as establishment_name_en',
                'establishments.nom_ar as establishment_name_ar',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('establishments.id', 'establishments.nom_fr', 'establishments.nom_en', 'establishments.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');

        return response()->json([
            'data' => $results->map(function($item) {
                return [
                    'establishment_id' => $item->establishment_id,
                    'nom_fr' => $item->establishment_name_fr ?: 'Non spécifié',
                    'nom_en' => $item->establishment_name_en ?: 'Not specified',
                    'nom_ar' => $item->establishment_name_ar ?: 'غير محدد',
                    'subscriber_count' => (int)$item->subscriber_count,
                    'total_amount' => (float)$item->total_amount
                ];
            }),
            'total_amount' => (float)$totalAmount
        ]);
    }

    /**
     * Get subscribers statistics by tranche kilometric for each used line
     */
    public function subscribersByKilometricRangePerLine(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'lineId' => 'nullable|exists:lines,id',
        ]);

        $query = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('lines', 'trips.id_line', '=', 'lines.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        // Apply filters
        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('startDate')) {
            $query->where('subscriptions.start_date', '>=', $request->input('startDate'));
        }

        if ($request->filled('endDate')) {
            $query->where('subscriptions.start_date', '<=', $request->input('endDate'));
        }

        if ($request->filled('lineId') && $request->input('lineId') !== 'all') {
            $query->where('trips.id_line', $request->input('lineId'));
        }

        $results = $query->select(
                'lines.id as line_id',
                'lines.nom_fr as line_name_fr',
                'lines.nom_en as line_name_en',
                'lines.nom_ar as line_name_ar',
                'lines.CODE_LINE as line_code',
                DB::raw('FLOOR(trips.number_of_km / 5) * 5 as km_range_start'),
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('lines.id', 'lines.nom_fr', 'lines.nom_en', 'lines.nom_ar', 'lines.CODE_LINE', DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->orderBy('lines.nom_fr')
            ->orderBy(DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->get();

        $lineStats = [];
        $grandTotalAmount = 0;
        $grandTotalSubscribers = 0;

        foreach ($results as $item) {
            $lineId = $item->line_id;
            $kmRangeEnd = (int)$item->km_range_start + 4;

            if (!isset($lineStats[$lineId])) {
                $lineStats[$lineId] = [
                    'line_id' => $lineId,
                    'line_name_fr' => $item->line_name_fr,
                    'line_name_en' => $item->line_name_en,
                    'line_name_ar' => $item->line_name_ar,
                    'line_code' => $item->line_code,
                    'kilometric_ranges' => [],
                    'line_total_subscribers' => 0,
                    'line_total_amount' => 0
                ];
            }

            $rangeData = [
                'kilometric_range' => $item->km_range_start . ' - ' . $kmRangeEnd . ' km',
                'km_range_start' => (int)$item->km_range_start,
                'km_range_end' => $kmRangeEnd,
                'subscriber_count' => (int)$item->subscriber_count,
                'total_amount' => (float)$item->total_amount,
                'average_amount_per_subscriber' => $item->subscriber_count > 0
                    ? round((float)$item->total_amount / (int)$item->subscriber_count, 2)
                    : 0
            ];

            $lineStats[$lineId]['kilometric_ranges'][] = $rangeData;
            $lineStats[$lineId]['line_total_subscribers'] += (int)$item->subscriber_count;
            $lineStats[$lineId]['line_total_amount'] += (float)$item->total_amount;

            $grandTotalSubscribers += (int)$item->subscriber_count;
            $grandTotalAmount += (float)$item->total_amount;
        }

        foreach ($lineStats as &$lineData) {
            $lineData['line_total_amount'] = round($lineData['line_total_amount'], 2);
            $lineData['percentage_of_total_subscribers'] = $grandTotalSubscribers > 0
                ? round(($lineData['line_total_subscribers'] / $grandTotalSubscribers) * 100, 2)
                : 0;
            $lineData['percentage_of_total_amount'] = $grandTotalAmount > 0
                ? round(($lineData['line_total_amount'] / $grandTotalAmount) * 100, 2)
                : 0;
            $lineData['average_amount_per_subscriber_line'] = $lineData['line_total_subscribers'] > 0
                ? round($lineData['line_total_amount'] / $lineData['line_total_subscribers'], 2)
                : 0;

            // Calculate percentages within each line for ranges
            foreach ($lineData['kilometric_ranges'] as &$range) {
                $range['percentage_within_line_subscribers'] = $lineData['line_total_subscribers'] > 0
                    ? round(($range['subscriber_count'] / $lineData['line_total_subscribers']) * 100, 2)
                    : 0;
                $range['percentage_within_line_amount'] = $lineData['line_total_amount'] > 0
                    ? round(($range['total_amount'] / $lineData['line_total_amount']) * 100, 2)
                    : 0;
            }
        }

        return response()->json([
            'data' => array_values($lineStats),
            'summary' => [
                'total_lines' => count($lineStats),
                'grand_total_subscribers' => $grandTotalSubscribers,
                'grand_total_amount' => round($grandTotalAmount, 2),
                'average_subscribers_per_line' => count($lineStats) > 0
                    ? round($grandTotalSubscribers / count($lineStats), 2)
                    : 0,
                'average_amount_per_line' => count($lineStats) > 0
                    ? round($grandTotalAmount / count($lineStats), 2)
                    : 0
            ]
        ]);
    }

    /**
     * Get subscribers statistics by discount
     * Returns statistics for each discount including subscription count and total amounts
     */
    public function subscribersByDiscount(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'discountId' => 'nullable|exists:discounts,id',
        ]);

        // Get all discounts with their basic information
        $allDiscounts = Discount::with('subsType')
            ->select('id', 'nom_fr', 'nom_en', 'nom_ar', 'percentage', 'is_stagiaire', 'special_client', 'id_subs_type')
            ->get();

        // Build query to find subscriptions that match discount criteria
        $subscriptionQuery = Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        // Apply filters
        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $subscriptionQuery->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('startDate')) {
            $subscriptionQuery->where('subscriptions.start_date', '>=', $request->input('startDate'));
        }

        if ($request->filled('endDate')) {
            $subscriptionQuery->where('subscriptions.start_date', '<=', $request->input('endDate'));
        }

        $discountStats = [];
        $grandTotalSubscribers = 0;
        $grandTotalAmount = 0;
        $grandTotalDiscountAmount = 0;

        foreach ($allDiscounts as $discount) {
            // Skip if discount filter is applied and doesn't match
            if ($request->filled('discountId') && $request->input('discountId') !== 'all' && $discount->id != $request->input('discountId')) {
                continue;
            }

            // Clone the base query for this discount
            $discountQuery = clone $subscriptionQuery;

            // Apply discount-specific criteria
            $discountQuery->where('subscriptions.id_subs_type', $discount->id_subs_type);

            if ($discount->is_stagiaire) {
                $discountQuery->where('subscriptions.is_stagiaire', true);
            }

            if ($discount->special_client) {
                $discountQuery->where('subscriptions.special_client', $discount->special_client);
            }

            // Check if subscription dates overlap with discount validity period
            $discountQuery->where(function($query) use ($discount) {
                $query->where(function($subQuery) use ($discount) {
                    $subQuery->where('subscriptions.start_date', '<=', $discount->date_end)
                             ->where('subscriptions.end_date', '>=', $discount->date_start);
                })->orWhere(function($subQuery) use ($discount) {
                    $subQuery->whereNull('subscriptions.start_date')
                             ->orWhereNull('subscriptions.end_date');
                });
            });

            $results = $discountQuery->select(
                    DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                    DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
                )
                ->first();

            $subscriberCount = (int)($results->subscriber_count ?? 0);
            $totalAmount = (float)($results->total_amount ?? 0);

            // Calculate discount amount
            $discountAmount = $totalAmount * ($discount->percentage / 100);
            $originalAmount = $totalAmount > 0 ? $totalAmount / (1 - ($discount->percentage / 100)) : 0;

            $discountData = [
                'discount_id' => $discount->id,
                'discount_name_fr' => $discount->nom_fr,
                'discount_name_en' => $discount->nom_en,
                'discount_name_ar' => $discount->nom_ar,
                'discount_percentage' => (float)$discount->percentage,
                'is_stagiaire' => $discount->is_stagiaire,
                'special_client' => $discount->special_client,
                'subs_type' => [
                    'id' => $discount->subsType->id ?? null,
                    'nom_fr' => $discount->subsType->nom_fr ?? null,
                    'nom_en' => $discount->subsType->nom_en ?? null,
                    'nom_ar' => $discount->subsType->nom_ar ?? null,
                ],
                'subscriber_count' => $subscriberCount,
                'total_amount_paid' => round($totalAmount, 2),
                'estimated_original_amount' => round($originalAmount, 2),
                'estimated_discount_amount' => round($discountAmount, 2),
                'average_amount_per_subscriber' => $subscriberCount > 0
                    ? round($totalAmount / $subscriberCount, 2)
                    : 0,
                'average_discount_per_subscriber' => $subscriberCount > 0
                    ? round($discountAmount / $subscriberCount, 2)
                    : 0
            ];

            // Only include discounts that have subscriptions or if no filter is applied
            if ($subscriberCount > 0 || !$request->filled('discountId')) {
                $discountStats[] = $discountData;
                $grandTotalSubscribers += $subscriberCount;
                $grandTotalAmount += $totalAmount;
                $grandTotalDiscountAmount += $discountAmount;
            }
        }

        // Calculate percentages
        foreach ($discountStats as &$discountData) {
            $discountData['percentage_of_total_subscribers'] = $grandTotalSubscribers > 0
                ? round(($discountData['subscriber_count'] / $grandTotalSubscribers) * 100, 2)
                : 0;
            $discountData['percentage_of_total_amount'] = $grandTotalAmount > 0
                ? round(($discountData['total_amount_paid'] / $grandTotalAmount) * 100, 2)
                : 0;
        }

        return response()->json([
            'data' => $discountStats,
            'summary' => [
                'total_discounts' => count($discountStats),
                'grand_total_subscribers' => $grandTotalSubscribers,
                'grand_total_amount_paid' => round($grandTotalAmount, 2),
                'grand_total_discount_amount' => round($grandTotalDiscountAmount, 2),
                'grand_estimated_original_amount' => round($grandTotalAmount + $grandTotalDiscountAmount, 2),
                'average_subscribers_per_discount' => count($discountStats) > 0
                    ? round($grandTotalSubscribers / count($discountStats), 2)
                    : 0,
                'average_amount_per_discount' => count($discountStats) > 0
                    ? round($grandTotalAmount / count($discountStats), 2)
                    : 0,
                'overall_discount_percentage' => ($grandTotalAmount + $grandTotalDiscountAmount) > 0
                    ? round(($grandTotalDiscountAmount / ($grandTotalAmount + $grandTotalDiscountAmount)) * 100, 2)
                    : 0
            ]
        ]);
    }

}
