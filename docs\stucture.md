# Database Schema Documentation

## Tables

### LINE
- **id**: number
- **name**: string
- **type_service**: enum(normal-confort)
- **active**: boolean
- **Commercial_speed**: number

### STATION
- **id**: number
- **name**: string
- **longiture**: number (likely typo: "longitude")
- **latitude**: number
- **type**: enum(terminus-inter-hidden)

### TRIP
- **id**: number
- **name**: string
- **id_LINE**: number (foreign key to LINE)
- **id_STATION_start**: number (foreign key to STATION)
- **id_STATION_end**: number (foreign key to STATION)
- **active**: boolean
- **inter_station**: boolean
- **number_of_km**: number

### TARIFF_BASE
- **id**: number
- **name**: string
- **tariffPerKM**: number
- **id_SUBS_TYPE**: number (foreign key to SUBS_TYPE)

### LINE_STATION
- **id**: number
- **id_LINE**: number (foreign key to LINE)
- **id_STATION**: number (foreign key to STATION)
- **position**: number
- **start_time**: array (nullable) (likely typo: "heures_depart")

### SUBS_TYPE
- **id**: number
- **name**: string
- **color**: string

### TARIFF_OPTIONS
- **id**: number
- **id_TRIP**: number (foreign key to TRIP)
- **id_SUBS_TYPE**: number (foreign key to TYPEABN)
- **is_regular**: boolean
- **id_TARIFF_BASE**: number (nullable, foreign key to BASE_TARIFAIRE)
- **manual_tariff**: number (nullable)

### GOVERNORATE
- **id**: number
- **name**: string

### DELEGATION
- **id**: number
- **name**: string
- **id_GOVERNORATE**: number (foreign key to GOVERNORATE)

### TYPE_ESTABLISHMENT
- **id**: number
- **name**: string

### ESTABLISHMENT
- **id**: number
- **name**: string
- **Abbrevation**: string
- **id_DELEGATION**: number (foreign key to DELEGATION)
- **id_TYPE_ESTABLISHMENT**: number (foreign key to TYPE_ESTABLISHMENT)

### NIVEAU_SCOLAIRE
- **id**: number
- **name**: string
- **age_max**: number
- **id_ESTABLISHMENT**: number (foreign key to ESTABLISHMENT)

### CAMPAIGNS
- **id**: number
- **name**: string
- **status**: boolean

### SALE_POINT
- **id**: number
- **name**: string

### ADMIN
- **id**: number
- **name**: string
- **id_SALE_POINT**: number (foreign key to SALE_POINT)

### SALE_PERIOD
- **id**: number
- **name**: string
- **date_debut**: date
- **date_fin**: date
- **status**: boolean

### AFFECTATION_AGENT
- **id**: number
- **id_AGENT**: number
- **id_SALE_POINT**: number (foreign key to SALE_POINT)
- **id_SALE_PERIOD**: number (foreign key to SALE_PERIOD)

### TYPE_CLIENT
- **id**: number
- **name**: string

### DISCOUNT
- **id**: number
- **name**: string
- **percentage**: number
- **id_PERIODICITE_TYPE**: number (foreign key to PERIODICITE)
- **id_CLIENT_TYPE**: number (foreign key to TYPE_CLIENT)

### PERIODICITE
- **id**: number
- **name**: string
- **number_days**: number

### MOTIF_DUPLICATE
- **id**: number
- **name**: string

### STOCK_CARDS
- **id**: number
- **name**: string
- **id_CARD_TYPE**: number (foreign key to CARD_TYPE)
- **initial_quantity**: number
- **status**: number

### CLIENT
- **id**: number
- **name**: string

### SUBSCRIPTION
- **id**: int

### PAYMENT_METHODS
- **id**: int

### SUBS_CARD
- **id**: int
- **id_CARD_TYPE**: number (foreign key to CARD_TYPE)
- **id_SALE_POINT**: number (foreign key to SALE_POINT)

### CARD_TYPE
- **id**: number
- **name**: string

### CARD_FEE
- **id**: number
- **name**: string
- **amount**: number

### AGENCIE
- **id**: number
- **name**: string

---

## Relationships

1. **LINE** ↔ **TRIP**  
   - `LINE 1` — `TRIP 1..*` (via `id_LINE`)

2. **STATION** ↔ **TRIP**  
   - `STATION 1` — `TRIP 1..*` (via `id_STATION_DEP` and `id_STATION_ARR`)

3. **LINE** ↔ **LINE_STATION**  
   - `LINE 1..*` — `LINE_STATION 1..*` (via `id_LINE`)

4. **STATION** ↔ **LINE_STATION**  
   - `STATION 1..*` — `LINE_STATION 1..*` (via `id_STATION`)

5. **SUBS_TYPE** ↔ **BASE_TARIFAIRE**  
   - `SUBS_TYPE 1..*` — `BASE_TARIFAIRE 1` (via `id_SUBS_TYPE`)

6. **TRIP** ↔ **TARIFF_OPTIONS**  
   - `TRIP 1..*` — `TARIFF_OPTIONS 1..*` (via `id_TRIP`)

7. **GOVERNORATE** ↔ **DELEGATION**  
   - `GOVERNORATE 1` — `DELEGATION 1..*` (via `id_GOVERNORATE`)

8. **ESTABLISHMENT** ↔ **DELEGATION**  
   - `ESTABLISHMENT 1..*` — `DELEGATION 1` (via `id_DELEGATION`)

9. **ESTABLISHMENT** ↔ **TYPE_ESTABLISHMENT**  
   - `ESTABLISHMENT 1..*` — `TYPE_ESTABLISHMENT 1` (via `id_TYPE_ESTABLISHMENT`)

10. **CAMPAGNE** ↔ **SALE_PERIOD**  
    - `CAMPAGNE 1` — `SALE_PERIOD 1..*` (via date fields)

11. **ADMIN** ↔ **AFFECTATION_AGENT**  
    - `ADMIN 1..*` — `AFFECTATION_AGENT 1..*` (via `id_AGENT`)

12. **SALE_POINT** ↔ **AFFECTATION_AGENT**  
    - `SALE_POINT 1..*` — `AFFECTATION_AGENT 1..*` (via `id_SALE_POINT`)

13. **CLIENT** ↔ **TYPE_CLIENT**  
    - `CLIENT 1..*` — `TYPE_CLIENT 1` (via foreign keys)

14. **CARD_TYPE** ↔ **STOCK_CARDS**  
    - `CARD_TYPE 1` — `STOCK_CARDS 1..*` (via `id_CARD_TYPE`)

15. **SUBS_CARD** ↔ **CARD_TYPE**  
    - `SUBS_CARD 1..*` — `CARD_TYPE 1` (via `id_CARD_TYPE`)

16. **AGENCIE** ↔ **DELEGATION**  
    - `AGENCIE 1..*` — `DELEGATION 1` (via foreign keys)